using FaceRecognitionMonitoring.Core.Configuration;
using FaceRecognitionMonitoring.Core.Interfaces;
using FaceRecognitionMonitoring.Infrastructure.Camera;
using FaceRecognitionMonitoring.Infrastructure.Configuration;
using FaceRecognitionMonitoring.Infrastructure.Exceptions;
using FaceRecognitionMonitoring.Infrastructure.FaceDetection;
using FaceRecognitionMonitoring.Infrastructure.Storage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Sentry;
using Serilog;

namespace FaceRecognitionMonitoring.Infrastructure.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加基础设施服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();

        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // 配置Sentry
        services.AddSentry(options =>
        {
            configuration.GetSection("Sentry").Bind(options);
        });

        // 注册配置
        services.Configure<CameraSettings>(configuration.GetSection("Camera"));
        services.Configure<FaceDetectionSettings>(configuration.GetSection("FaceDetection"));
        services.Configure<StorageSettings>(configuration.GetSection("Storage"));
        services.Configure<UISettings>(configuration.GetSection("UI"));

        // 注册可写用户设置
        services.AddSingleton<IWritableOptions<UserSettings>>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<WritableOptions<UserSettings>>>();
            var userSettingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "userSettings.json");
            var defaultSettings = new UserSettings();
            return new WritableOptions<UserSettings>(logger, userSettingsPath, defaultSettings);
        });

        // 注册核心服务
        services.AddSingleton<ICameraProvider, OpenCvCameraProvider>();
        services.AddSingleton<IFaceDetectionService, YunetFaceDetectionService>();
        services.AddSingleton<IImageStorageService, FileImageStorageService>();

        // 注册异常处理
        services.AddSingleton<GlobalExceptionHandler>();

        return services;
    }

    /// <summary>
    /// 添加日志配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCustomLogging(this IServiceCollection services, IConfiguration configuration)
    {
        // 确保日志目录存在
        var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        Directory.CreateDirectory(logPath);

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(
                path: Path.Combine(logPath, "app-.log"),
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(dispose: true);
        });

        return services;
    }

    /// <summary>
    /// 添加监控服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMonitoring(this IServiceCollection services, IConfiguration configuration)
    {
        var sentryDsn = configuration["Sentry:Dsn"];
        
        if (!string.IsNullOrEmpty(sentryDsn))
        {
            services.AddSentry(options =>
            {
                options.Dsn = sentryDsn;
                options.Environment = configuration["Sentry:Environment"] ?? "Development";
                options.TracesSampleRate = configuration.GetValue<double>("Sentry:TracesSampleRate", 1.0);
                options.Debug = configuration.GetValue<bool>("Sentry:Debug", false);
                options.AutoSessionTracking = true;
                options.IsGlobalModeEnabled = true;
            });
        }

        return services;
    }
}
