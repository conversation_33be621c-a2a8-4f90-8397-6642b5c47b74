namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 分辨率信息
/// </summary>
public record Resolution
{
    /// <summary>
    /// 宽度
    /// </summary>
    public required int Width { get; init; }
    
    /// <summary>
    /// 高度
    /// </summary>
    public required int Height { get; init; }
    
    /// <summary>
    /// 宽高比
    /// </summary>
    public double AspectRatio => (double)Width / Height;
    
    /// <summary>
    /// 像素总数
    /// </summary>
    public int PixelCount => Width * Height;
    
    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName => $"{Width}x{Height}";
    
    /// <summary>
    /// 常用分辨率预设
    /// </summary>
    public static class Presets
    {
        public static readonly Resolution HD = new() { Width = 1280, Height = 720 };
        public static readonly Resolution FullHD = new() { Width = 1920, Height = 1080 };
        public static readonly Resolution QHD = new() { Width = 2560, Height = 1440 };
        public static readonly Resolution UHD = new() { Width = 3840, Height = 2160 };
        public static readonly Resolution VGA = new() { Width = 640, Height = 480 };
    }
    
    public override string ToString() => DisplayName;
}
