namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 人脸区域信息
/// </summary>
public record FaceRegion
{
    /// <summary>
    /// 边界框X坐标
    /// </summary>
    public required float X { get; init; }
    
    /// <summary>
    /// 边界框Y坐标
    /// </summary>
    public required float Y { get; init; }
    
    /// <summary>
    /// 边界框宽度
    /// </summary>
    public required float Width { get; init; }
    
    /// <summary>
    /// 边界框高度
    /// </summary>
    public required float Height { get; init; }
    
    /// <summary>
    /// 置信度分数
    /// </summary>
    public required float Confidence { get; init; }
    
    /// <summary>
    /// 人脸关键点（可选）
    /// </summary>
    public IReadOnlyList<Point2D>? Landmarks { get; init; }
    
    /// <summary>
    /// 中心点
    /// </summary>
    public Point2D Center => new() { X = X + Width / 2, Y = Y + Height / 2 };
    
    /// <summary>
    /// 面积
    /// </summary>
    public float Area => Width * Height;
}
