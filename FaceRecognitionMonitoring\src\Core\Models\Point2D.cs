namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 二维点坐标
/// </summary>
public record Point2D
{
    /// <summary>
    /// X坐标
    /// </summary>
    public required float X { get; init; }
    
    /// <summary>
    /// Y坐标
    /// </summary>
    public required float Y { get; init; }
    
    /// <summary>
    /// 计算到另一个点的距离
    /// </summary>
    /// <param name="other">另一个点</param>
    /// <returns>距离</returns>
    public double DistanceTo(Point2D other)
    {
        var dx = X - other.X;
        var dy = Y - other.Y;
        return Math.Sqrt(dx * dx + dy * dy);
    }
    
    public override string ToString() => $"({X:F1}, {Y:F1})";
}
