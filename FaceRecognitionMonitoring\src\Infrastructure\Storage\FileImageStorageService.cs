using FaceRecognitionMonitoring.Core.Configuration;
using FaceRecognitionMonitoring.Core.Interfaces;
using FaceRecognitionMonitoring.Core.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OpenCvSharp;
using System.Text.Json;

namespace FaceRecognitionMonitoring.Infrastructure.Storage;

/// <summary>
/// 文件系统图像存储服务实现
/// </summary>
public class FileImageStorageService : IImageStorageService
{
    private readonly ILogger<FileImageStorageService> _logger;
    private readonly IWritableOptions<UserSettings> _userSettings;
    private readonly StorageSettings _storageSettings;

    public FileImageStorageService(
        ILogger<FileImageStorageService> logger,
        IWritableOptions<UserSettings> userSettings,
        IOptions<StorageSettings> storageSettings)
    {
        _logger = logger;
        _userSettings = userSettings;
        _storageSettings = storageSettings.Value;
    }

    public async Task<CapturedImage> SaveImageAsync(byte[] imageData, string cameraName, int faceCount, Resolution resolution, CancellationToken cancellationToken = default)
    {
        try
        {
            var captureTime = DateTime.Now;
            var fileName = $"{captureTime.ToString(_storageSettings.FileNameFormat)}.{_storageSettings.DefaultFormat}";
            var cameraDir = Path.Combine(_userSettings.Value.SavePath, SanitizeFileName(cameraName));
            
            // 确保目录存在
            Directory.CreateDirectory(cameraDir);
            
            var filePath = Path.Combine(cameraDir, fileName);
            
            // 保存图像
            await File.WriteAllBytesAsync(filePath, imageData, cancellationToken);
            
            var fileInfo = new FileInfo(filePath);
            
            var capturedImage = new CapturedImage
            {
                FilePath = filePath,
                CaptureTime = captureTime,
                CameraName = cameraName,
                Resolution = resolution,
                FileSize = fileInfo.Length,
                FaceCount = faceCount
            };
            
            _logger.LogInformation("图像已保存: {FileName} ({FileSize}, {FaceCount}个人脸)", 
                fileName, capturedImage.FormattedFileSize, faceCount);
            
            // 异步生成缩略图
            _ = Task.Run(async () =>
            {
                try
                {
                    await GenerateThumbnailAsync(filePath, 200, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "生成缩略图失败: {FilePath}", filePath);
                }
            }, cancellationToken);
            
            return capturedImage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存图像时发生错误");
            throw;
        }
    }

    public async Task<IReadOnlyList<CapturedImage>> GetImagesAsync(string? cameraName = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var images = new List<CapturedImage>();
            var basePath = _userSettings.Value.SavePath;
            
            if (!Directory.Exists(basePath))
            {
                return images;
            }
            
            var searchPattern = $"*.{_storageSettings.DefaultFormat}";
            var searchOption = SearchOption.AllDirectories;
            
            if (!string.IsNullOrEmpty(cameraName))
            {
                var cameraDir = Path.Combine(basePath, SanitizeFileName(cameraName));
                if (Directory.Exists(cameraDir))
                {
                    basePath = cameraDir;
                    searchOption = SearchOption.TopDirectoryOnly;
                }
                else
                {
                    return images;
                }
            }
            
            var files = Directory.GetFiles(basePath, searchPattern, searchOption);
            
            foreach (var file in files)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                try
                {
                    var image = await CreateCapturedImageFromFileAsync(file, cancellationToken);
                    if (image != null)
                    {
                        images.Add(image);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "处理图像文件时发生错误: {FilePath}", file);
                }
            }
            
            return images.OrderByDescending(i => i.CaptureTime).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取图像列表时发生错误");
            return Array.Empty<CapturedImage>();
        }
    }

    public async Task<IReadOnlyList<CapturedImage>> GetRecentImagesAsync(int count, string? cameraName = null, CancellationToken cancellationToken = default)
    {
        var allImages = await GetImagesAsync(cameraName, cancellationToken);
        return allImages.Take(count).ToList();
    }

    public async Task<IReadOnlyList<CapturedImage>> SearchImagesAsync(string searchTerm, CancellationToken cancellationToken = default)
    {
        var allImages = await GetImagesAsync(null, cancellationToken);
        
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            return allImages;
        }
        
        var searchTermLower = searchTerm.ToLowerInvariant();
        
        return allImages.Where(image =>
            image.FileName.ToLowerInvariant().Contains(searchTermLower) ||
            image.CameraName.ToLowerInvariant().Contains(searchTermLower)
        ).ToList();
    }

    public async Task<bool> DeleteImageAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("要删除的文件不存在: {FilePath}", filePath);
                return false;
            }
            
            File.Delete(filePath);
            
            // 同时删除缩略图
            var thumbnailPath = GetThumbnailPath(filePath);
            if (File.Exists(thumbnailPath))
            {
                File.Delete(thumbnailPath);
            }
            
            _logger.LogInformation("图像已删除: {FilePath}", filePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除图像时发生错误: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<string?> GenerateThumbnailAsync(string imagePath, int thumbnailSize = 200, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(imagePath))
            {
                return null;
            }
            
            var thumbnailPath = GetThumbnailPath(imagePath);
            var thumbnailDir = Path.GetDirectoryName(thumbnailPath);
            
            if (!string.IsNullOrEmpty(thumbnailDir))
            {
                Directory.CreateDirectory(thumbnailDir);
            }
            
            using var originalImage = Cv2.ImRead(imagePath);
            if (originalImage.Empty())
            {
                return null;
            }
            
            // 计算缩略图尺寸，保持宽高比
            var scale = Math.Min((double)thumbnailSize / originalImage.Width, (double)thumbnailSize / originalImage.Height);
            var newWidth = (int)(originalImage.Width * scale);
            var newHeight = (int)(originalImage.Height * scale);
            
            using var thumbnail = new Mat();
            Cv2.Resize(originalImage, thumbnail, new Size(newWidth, newHeight));
            
            Cv2.ImWrite(thumbnailPath, thumbnail);
            
            return thumbnailPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成缩略图时发生错误: {ImagePath}", imagePath);
            return null;
        }
    }

    public async Task<int> CleanupOldFilesAsync(int retentionDays, CancellationToken cancellationToken = default)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-retentionDays);
            var deletedCount = 0;
            var basePath = _userSettings.Value.SavePath;
            
            if (!Directory.Exists(basePath))
            {
                return 0;
            }
            
            var files = Directory.GetFiles(basePath, $"*.{_storageSettings.DefaultFormat}", SearchOption.AllDirectories);
            
            foreach (var file in files)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                try
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        await DeleteImageAsync(file, cancellationToken);
                        deletedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "清理文件时发生错误: {FilePath}", file);
                }
            }
            
            _logger.LogInformation("清理完成，删除了 {DeletedCount} 个过期文件", deletedCount);
            return deletedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期文件时发生错误");
            return 0;
        }
    }

    private async Task<CapturedImage?> CreateCapturedImageFromFileAsync(string filePath, CancellationToken cancellationToken)
    {
        try
        {
            var fileInfo = new FileInfo(filePath);
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            
            // 尝试从文件名解析时间
            DateTime captureTime;
            if (!DateTime.TryParseExact(fileName, _storageSettings.FileNameFormat, null, System.Globalization.DateTimeStyles.None, out captureTime))
            {
                captureTime = fileInfo.CreationTime;
            }
            
            // 从路径获取摄像头名称
            var cameraName = Path.GetFileName(Path.GetDirectoryName(filePath)) ?? "Unknown";
            
            // 获取图像分辨率
            Resolution resolution;
            try
            {
                using var image = Cv2.ImRead(filePath);
                resolution = new Resolution { Width = image.Width, Height = image.Height };
            }
            catch
            {
                resolution = new Resolution { Width = 0, Height = 0 };
            }
            
            var thumbnailPath = GetThumbnailPath(filePath);
            
            return new CapturedImage
            {
                FilePath = filePath,
                CaptureTime = captureTime,
                CameraName = cameraName,
                Resolution = resolution,
                FileSize = fileInfo.Length,
                FaceCount = 0, // 无法从文件直接获取，需要重新检测
                ThumbnailPath = File.Exists(thumbnailPath) ? thumbnailPath : null
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "创建CapturedImage对象时发生错误: {FilePath}", filePath);
            return null;
        }
    }

    private string GetThumbnailPath(string imagePath)
    {
        var directory = Path.GetDirectoryName(imagePath) ?? "";
        var fileName = Path.GetFileNameWithoutExtension(imagePath);
        var extension = Path.GetExtension(imagePath);
        
        var thumbnailDir = Path.Combine(directory, "thumbnails");
        return Path.Combine(thumbnailDir, $"{fileName}_thumb{extension}");
    }

    private string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
    }
}
