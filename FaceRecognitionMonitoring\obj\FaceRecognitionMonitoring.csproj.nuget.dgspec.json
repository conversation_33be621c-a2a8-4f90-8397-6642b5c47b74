{"format": 1, "restore": {"E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj": {}}, "projects": {"E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj", "projectName": "FaceRecognitionMonitoring", "projectPath": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "FluentAvalonia.UI": {"target": "Package", "version": "[2.0.5, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "ModernWpfUI": {"target": "Package", "version": "[0.9.6, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "OpenCvSharp4": {"target": "Package", "version": "[4.9.0.20240103, )"}, "OpenCvSharp4.runtime.win": {"target": "Package", "version": "[4.9.0.20240103, )"}, "Sentry": {"target": "Package", "version": "[4.0.2, )"}, "Sentry.Serilog": {"target": "Package", "version": "[4.0.2, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Threading.Channels": {"target": "Package", "version": "[8.0.0, )"}, "xunit": {"target": "Package", "version": "[2.6.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-rc.1.25451.107/PortableRuntimeIdentifierGraph.json"}}}}}