using FaceRecognitionMonitoring.Core.Enums;

namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 摄像头信息
/// </summary>
public record CameraInfo
{
    /// <summary>
    /// 摄像头ID
    /// </summary>
    public required int Id { get; init; }
    
    /// <summary>
    /// 摄像头名称
    /// </summary>
    public required string Name { get; init; }
    
    /// <summary>
    /// 摄像头描述
    /// </summary>
    public string? Description { get; init; }
    
    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; init; } = true;
    
    /// <summary>
    /// 当前状态
    /// </summary>
    public CameraStatus Status { get; init; } = CameraStatus.Disconnected;
    
    /// <summary>
    /// 支持的分辨率列表
    /// </summary>
    public IReadOnlyList<Resolution> SupportedResolutions { get; init; } = Array.Empty<Resolution>();
    
    /// <summary>
    /// 当前分辨率
    /// </summary>
    public Resolution? CurrentResolution { get; init; }
    
    /// <summary>
    /// 当前帧率
    /// </summary>
    public double CurrentFrameRate { get; init; }
}
