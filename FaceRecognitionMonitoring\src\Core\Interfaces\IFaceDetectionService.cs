using FaceRecognitionMonitoring.Core.Models;

namespace FaceRecognitionMonitoring.Core.Interfaces;

/// <summary>
/// 人脸检测服务接口
/// </summary>
public interface IFaceDetectionService : IDisposable
{
    /// <summary>
    /// 初始化检测模型
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeAsync(string modelPath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 检测图像中的人脸
    /// </summary>
    /// <param name="imageData">图像数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检测结果</returns>
    Task<FaceDetectionResult> DetectAsync(byte[] imageData, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 批量检测图像中的人脸
    /// </summary>
    /// <param name="imageDataList">图像数据列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>检测结果列表</returns>
    Task<IReadOnlyList<FaceDetectionResult>> DetectBatchAsync(IReadOnlyList<byte[]> imageDataList, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 设置检测参数
    /// </summary>
    /// <param name="confidenceThreshold">置信度阈值</param>
    /// <param name="nmsThreshold">非极大值抑制阈值</param>
    /// <param name="topK">最大检测数量</param>
    void SetParameters(float confidenceThreshold, float nmsThreshold, int topK);
    
    /// <summary>
    /// 是否已初始化
    /// </summary>
    bool IsInitialized { get; }
    
    /// <summary>
    /// 当前置信度阈值
    /// </summary>
    float ConfidenceThreshold { get; }
    
    /// <summary>
    /// 当前NMS阈值
    /// </summary>
    float NmsThreshold { get; }
    
    /// <summary>
    /// 最大检测数量
    /// </summary>
    int TopK { get; }
}
