using FaceRecognitionMonitoring.Core.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace FaceRecognitionMonitoring.Infrastructure.Configuration;

/// <summary>
/// 可写配置选项实现
/// </summary>
/// <typeparam name="T">配置类型</typeparam>
public class WritableOptions<T> : IWritableOptions<T> where T : class
{
    private readonly ILogger<WritableOptions<T>> _logger;
    private readonly string _filePath;
    private readonly JsonSerializerOptions _jsonOptions;
    private T _value;
    private readonly object _lock = new();

    public WritableOptions(
        ILogger<WritableOptions<T>> logger,
        string filePath,
        T defaultValue)
    {
        _logger = logger;
        _filePath = filePath;
        _value = defaultValue;
        
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
        
        // 尝试加载现有配置
        _ = Task.Run(async () =>
        {
            try
            {
                await ReloadAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "初始化时加载配置失败，使用默认配置");
            }
        });
    }

    public T Value
    {
        get
        {
            lock (_lock)
            {
                return _value;
            }
        }
    }

    public event EventHandler<T>? OnChange;

    public async Task UpdateAsync(Action<T> updateAction, CancellationToken cancellationToken = default)
    {
        T oldValue;
        T newValue;
        
        lock (_lock)
        {
            oldValue = _value;
            
            // 创建副本进行更新
            var json = JsonSerializer.Serialize(_value, _jsonOptions);
            newValue = JsonSerializer.Deserialize<T>(json, _jsonOptions) ?? _value;
            
            updateAction(newValue);
            _value = newValue;
        }
        
        try
        {
            await SaveAsync(cancellationToken);
            
            // 触发变化事件
            OnChange?.Invoke(this, newValue);
            
            _logger.LogDebug("配置已更新: {ConfigType}", typeof(T).Name);
        }
        catch (Exception ex)
        {
            // 回滚更改
            lock (_lock)
            {
                _value = oldValue;
            }
            
            _logger.LogError(ex, "保存配置时发生错误，已回滚更改");
            throw;
        }
    }

    public async Task SaveAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            T currentValue;
            lock (_lock)
            {
                currentValue = _value;
            }
            
            var directory = Path.GetDirectoryName(_filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            var json = JsonSerializer.Serialize(currentValue, _jsonOptions);
            await File.WriteAllTextAsync(_filePath, json, cancellationToken);
            
            _logger.LogDebug("配置已保存到文件: {FilePath}", _filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存配置文件时发生错误: {FilePath}", _filePath);
            throw;
        }
    }

    public async Task ReloadAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(_filePath))
            {
                _logger.LogDebug("配置文件不存在，使用默认配置: {FilePath}", _filePath);
                return;
            }
            
            var json = await File.ReadAllTextAsync(_filePath, cancellationToken);
            
            if (string.IsNullOrWhiteSpace(json))
            {
                _logger.LogWarning("配置文件为空: {FilePath}", _filePath);
                return;
            }
            
            var loadedValue = JsonSerializer.Deserialize<T>(json, _jsonOptions);
            
            if (loadedValue != null)
            {
                T oldValue;
                lock (_lock)
                {
                    oldValue = _value;
                    _value = loadedValue;
                }
                
                // 触发变化事件
                OnChange?.Invoke(this, loadedValue);
                
                _logger.LogDebug("配置已从文件重新加载: {FilePath}", _filePath);
            }
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "解析配置文件时发生错误: {FilePath}", _filePath);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载配置文件时发生错误: {FilePath}", _filePath);
            throw;
        }
    }
}
