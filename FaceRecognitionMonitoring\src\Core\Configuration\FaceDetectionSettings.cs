namespace FaceRecognitionMonitoring.Core.Configuration;

/// <summary>
/// 人脸检测配置
/// </summary>
public class FaceDetectionSettings
{
    /// <summary>
    /// 模型文件路径
    /// </summary>
    public string ModelPath { get; set; } = "assets/face_detection/yunet.onnx";
    
    /// <summary>
    /// 置信度阈值
    /// </summary>
    public float ConfidenceThreshold { get; set; } = 0.6f;
    
    /// <summary>
    /// 非极大值抑制阈值
    /// </summary>
    public float NmsThreshold { get; set; } = 0.3f;
    
    /// <summary>
    /// 最大检测数量
    /// </summary>
    public int TopK { get; set; } = 5000;
    
    /// <summary>
    /// 冷却时间（秒）
    /// </summary>
    public int CooldownSeconds { get; set; } = 3;
    
    /// <summary>
    /// 最小连续帧数
    /// </summary>
    public int MinConsecutiveFrames { get; set; } = 5;
}
