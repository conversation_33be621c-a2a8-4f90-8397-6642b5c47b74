using FaceRecognitionMonitoring.Core.Enums;
using FaceRecognitionMonitoring.Core.Interfaces;
using FaceRecognitionMonitoring.Core.Models;
using Microsoft.Extensions.Logging;
using OpenCvSharp;
using System.Runtime.CompilerServices;
using System.Threading.Channels;

namespace FaceRecognitionMonitoring.Infrastructure.Camera;

/// <summary>
/// OpenCV摄像头提供者实现
/// </summary>
public class OpenCvCameraProvider : ICameraProvider
{
    private readonly ILogger<OpenCvCameraProvider> _logger;
    private VideoCapture? _capture;
    private CameraInfo? _currentCamera;
    private bool _isStreaming;
    private CancellationTokenSource? _streamCancellationTokenSource;
    private readonly Channel<byte[]> _frameChannel;
    private readonly ChannelWriter<byte[]> _frameWriter;
    private readonly ChannelReader<byte[]> _frameReader;
    private bool _disposed;

    public OpenCvCameraProvider(ILogger<OpenCvCameraProvider> logger)
    {
        _logger = logger;
        
        // 创建无界通道用于帧传输
        var channel = Channel.CreateUnbounded<byte[]>();
        _frameChannel = channel;
        _frameWriter = channel.Writer;
        _frameReader = channel.Reader;
    }

    public CameraInfo? CurrentCamera => _currentCamera;
    public bool IsConnected => _capture?.IsOpened() == true;
    public bool IsStreaming => _isStreaming;

    public event EventHandler<CameraInfo>? CameraStatusChanged;
    public event EventHandler? CameraListChanged;

    public async Task<IReadOnlyList<CameraInfo>> GetAvailableCamerasAsync(CancellationToken cancellationToken = default)
    {
        var cameras = new List<CameraInfo>();
        
        try
        {
            // 尝试检测最多10个摄像头
            for (int i = 0; i < 10; i++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                using var testCapture = new VideoCapture(i);
                if (testCapture.IsOpened())
                {
                    var width = (int)testCapture.Get(VideoCaptureProperties.FrameWidth);
                    var height = (int)testCapture.Get(VideoCaptureProperties.FrameHeight);
                    var fps = testCapture.Get(VideoCaptureProperties.Fps);
                    
                    var supportedResolutions = new List<Resolution>
                    {
                        Resolution.Presets.VGA,
                        Resolution.Presets.HD,
                        Resolution.Presets.FullHD
                    };
                    
                    var camera = new CameraInfo
                    {
                        Id = i,
                        Name = $"Camera {i}",
                        Description = $"USB Camera {i}",
                        IsAvailable = true,
                        Status = CameraStatus.Disconnected,
                        SupportedResolutions = supportedResolutions,
                        CurrentResolution = new Resolution { Width = width, Height = height },
                        CurrentFrameRate = fps
                    };
                    
                    cameras.Add(camera);
                    _logger.LogDebug("发现摄像头: {CameraName} ({Width}x{Height}@{Fps}fps)", 
                        camera.Name, width, height, fps);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用摄像头列表时发生错误");
        }
        
        return cameras;
    }

    public async Task<bool> ConnectAsync(int cameraId, Resolution? resolution = null, double frameRate = 30, CancellationToken cancellationToken = default)
    {
        try
        {
            await DisconnectAsync(cancellationToken);
            
            _capture = new VideoCapture(cameraId);
            
            if (!_capture.IsOpened())
            {
                _logger.LogWarning("无法打开摄像头 {CameraId}", cameraId);
                return false;
            }
            
            // 设置分辨率
            if (resolution != null)
            {
                _capture.Set(VideoCaptureProperties.FrameWidth, resolution.Width);
                _capture.Set(VideoCaptureProperties.FrameHeight, resolution.Height);
            }
            
            // 设置帧率
            _capture.Set(VideoCaptureProperties.Fps, frameRate);
            
            // 获取实际设置的参数
            var actualWidth = (int)_capture.Get(VideoCaptureProperties.FrameWidth);
            var actualHeight = (int)_capture.Get(VideoCaptureProperties.FrameHeight);
            var actualFps = _capture.Get(VideoCaptureProperties.Fps);
            
            _currentCamera = new CameraInfo
            {
                Id = cameraId,
                Name = $"Camera {cameraId}",
                Description = $"USB Camera {cameraId}",
                IsAvailable = true,
                Status = CameraStatus.Connected,
                CurrentResolution = new Resolution { Width = actualWidth, Height = actualHeight },
                CurrentFrameRate = actualFps
            };
            
            _logger.LogInformation("成功连接摄像头 {CameraId}: {Width}x{Height}@{Fps}fps", 
                cameraId, actualWidth, actualHeight, actualFps);
            
            CameraStatusChanged?.Invoke(this, _currentCamera);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接摄像头 {CameraId} 时发生错误", cameraId);
            return false;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await StopStreamAsync(cancellationToken);
            
            if (_capture != null)
            {
                _capture.Dispose();
                _capture = null;
            }
            
            if (_currentCamera != null)
            {
                _currentCamera = _currentCamera with { Status = CameraStatus.Disconnected };
                CameraStatusChanged?.Invoke(this, _currentCamera);
                _currentCamera = null;
            }
            
            _logger.LogInformation("摄像头已断开连接");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开摄像头连接时发生错误");
        }
    }

    public async IAsyncEnumerable<byte[]> StartStreamAsync([EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        if (_capture == null || !_capture.IsOpened())
        {
            _logger.LogWarning("摄像头未连接，无法开始流式传输");
            yield break;
        }
        
        _isStreaming = true;
        _streamCancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        
        if (_currentCamera != null)
        {
            _currentCamera = _currentCamera with { Status = CameraStatus.Running };
            CameraStatusChanged?.Invoke(this, _currentCamera);
        }
        
        // 启动帧捕获任务
        _ = Task.Run(async () => await CaptureFramesAsync(_streamCancellationTokenSource.Token), _streamCancellationTokenSource.Token);
        
        // 从通道读取帧数据
        await foreach (var frameData in _frameReader.ReadAllAsync(cancellationToken))
        {
            yield return frameData;
        }
    }

    private async Task CaptureFramesAsync(CancellationToken cancellationToken)
    {
        using var frame = new Mat();
        
        try
        {
            while (!cancellationToken.IsCancellationRequested && _capture?.IsOpened() == true)
            {
                if (_capture.Read(frame) && !frame.Empty())
                {
                    var frameData = frame.ToBytes(".jpg");
                    
                    if (!_frameWriter.TryWrite(frameData))
                    {
                        _logger.LogWarning("帧缓冲区已满，丢弃帧");
                    }
                }
                else
                {
                    _logger.LogWarning("读取帧失败");
                    await Task.Delay(100, cancellationToken);
                }
                
                // 控制帧率
                await Task.Delay(33, cancellationToken); // ~30fps
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "捕获帧时发生错误");
        }
        finally
        {
            _frameWriter.TryComplete();
        }
    }

    public async Task StopStreamAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _isStreaming = false;
            
            _streamCancellationTokenSource?.Cancel();
            _streamCancellationTokenSource?.Dispose();
            _streamCancellationTokenSource = null;
            
            if (_currentCamera != null)
            {
                _currentCamera = _currentCamera with { Status = CameraStatus.Connected };
                CameraStatusChanged?.Invoke(this, _currentCamera);
            }
            
            _logger.LogInformation("视频流已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止视频流时发生错误");
        }
    }

    public async Task<byte[]?> CaptureFrameAsync(CancellationToken cancellationToken = default)
    {
        if (_capture == null || !_capture.IsOpened())
        {
            _logger.LogWarning("摄像头未连接，无法捕获帧");
            return null;
        }
        
        try
        {
            using var frame = new Mat();
            if (_capture.Read(frame) && !frame.Empty())
            {
                return frame.ToBytes(".jpg");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "捕获单帧时发生错误");
        }
        
        return null;
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        try
        {
            DisconnectAsync().Wait(200);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放摄像头资源时发生错误");
        }
        
        _disposed = true;
    }
}
