using FaceRecognitionMonitoring.Core.Enums;

namespace FaceRecognitionMonitoring.Core.Configuration;

/// <summary>
/// 用户个性化设置
/// </summary>
public class UserSettings
{
    /// <summary>
    /// 保存路径
    /// </summary>
    public string SavePath { get; set; } = "Captures";
    
    /// <summary>
    /// 冷却时间（秒）
    /// </summary>
    public int CooldownSeconds { get; set; } = 3;
    
    /// <summary>
    /// 应用主题
    /// </summary>
    public AppTheme Theme { get; set; } = AppTheme.System;
    
    /// <summary>
    /// 上次选择的摄像头ID
    /// </summary>
    public int? LastSelectedCameraId { get; set; }
    
    /// <summary>
    /// 窗口位置X
    /// </summary>
    public double? WindowX { get; set; }
    
    /// <summary>
    /// 窗口位置Y
    /// </summary>
    public double? WindowY { get; set; }
    
    /// <summary>
    /// 窗口宽度
    /// </summary>
    public double? WindowWidth { get; set; }
    
    /// <summary>
    /// 窗口高度
    /// </summary>
    public double? WindowHeight { get; set; }
    
    /// <summary>
    /// 窗口状态
    /// </summary>
    public string? WindowState { get; set; }
    
    /// <summary>
    /// 是否启用自动拍照
    /// </summary>
    public bool AutoCaptureEnabled { get; set; } = true;
    
    /// <summary>
    /// 是否显示检测框
    /// </summary>
    public bool ShowDetectionBoxes { get; set; } = true;
    
    /// <summary>
    /// 图像质量设置
    /// </summary>
    public int ImageQuality { get; set; } = 95;
}
