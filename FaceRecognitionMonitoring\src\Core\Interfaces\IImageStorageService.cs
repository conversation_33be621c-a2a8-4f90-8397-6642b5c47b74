using FaceRecognitionMonitoring.Core.Models;

namespace FaceRecognitionMonitoring.Core.Interfaces;

/// <summary>
/// 图像存储服务接口
/// </summary>
public interface IImageStorageService
{
    /// <summary>
    /// 保存图像
    /// </summary>
    /// <param name="imageData">图像数据</param>
    /// <param name="cameraName">摄像头名称</param>
    /// <param name="faceCount">检测到的人脸数量</param>
    /// <param name="resolution">图像分辨率</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存的图像信息</returns>
    Task<CapturedImage> SaveImageAsync(byte[] imageData, string cameraName, int faceCount, Resolution resolution, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取指定摄像头的所有图像
    /// </summary>
    /// <param name="cameraName">摄像头名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图像列表</returns>
    Task<IReadOnlyList<CapturedImage>> GetImagesAsync(string? cameraName = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取最近的图像
    /// </summary>
    /// <param name="count">数量</param>
    /// <param name="cameraName">摄像头名称（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图像列表</returns>
    Task<IReadOnlyList<CapturedImage>> GetRecentImagesAsync(int count, string? cameraName = null, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 搜索图像
    /// </summary>
    /// <param name="searchTerm">搜索关键词</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的图像列表</returns>
    Task<IReadOnlyList<CapturedImage>> SearchImagesAsync(string searchTerm, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 删除图像
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteImageAsync(string filePath, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 生成缩略图
    /// </summary>
    /// <param name="imagePath">原图路径</param>
    /// <param name="thumbnailSize">缩略图尺寸</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>缩略图路径</returns>
    Task<string?> GenerateThumbnailAsync(string imagePath, int thumbnailSize = 200, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 清理过期文件
    /// </summary>
    /// <param name="retentionDays">保留天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的文件数量</returns>
    Task<int> CleanupOldFilesAsync(int retentionDays, CancellationToken cancellationToken = default);
}
