{"format": 1, "restore": {"E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj": {}}, "projects": {"E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj", "projectName": "FaceRecognitionMonitoring", "projectPath": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\FaceRecognitionMonitoring.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Demo\\人脸监控抓取\\FaceRecognitionMonitoring\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-rc.1.25451.107/PortableRuntimeIdentifierGraph.json"}}}}}