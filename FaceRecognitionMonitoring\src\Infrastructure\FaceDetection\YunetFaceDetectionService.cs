using FaceRecognitionMonitoring.Core.Enums;
using FaceRecognitionMonitoring.Core.Interfaces;
using FaceRecognitionMonitoring.Core.Models;
using Microsoft.Extensions.Logging;
using OpenCvSharp;
using OpenCvSharp.Dnn;
using System.Diagnostics;

namespace FaceRecognitionMonitoring.Infrastructure.FaceDetection;

/// <summary>
/// Yunet人脸检测服务实现
/// </summary>
public class YunetFaceDetectionService : IFaceDetectionService
{
    private readonly ILogger<YunetFaceDetectionService> _logger;
    private Net? _net;
    private bool _isInitialized;
    private float _confidenceThreshold = 0.6f;
    private float _nmsThreshold = 0.3f;
    private int _topK = 5000;
    private bool _disposed;

    public YunetFaceDetectionService(ILogger<YunetFaceDetectionService> logger)
    {
        _logger = logger;
    }

    public bool IsInitialized => _isInitialized;
    public float ConfidenceThreshold => _confidenceThreshold;
    public float NmsThreshold => _nmsThreshold;
    public int TopK => _topK;

    public async Task<bool> InitializeAsync(string modelPath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!File.Exists(modelPath))
            {
                _logger.LogError("模型文件不存在: {ModelPath}", modelPath);
                return false;
            }

            _logger.LogInformation("正在加载人脸检测模型: {ModelPath}", modelPath);

            _net = CvDnn.ReadNetFromONNX(modelPath);
            
            if (_net.Empty())
            {
                _logger.LogError("无法加载模型文件: {ModelPath}", modelPath);
                return false;
            }

            // 设置计算后端
            _net.SetPreferableBackend(Backend.OPENCV);
            _net.SetPreferableTarget(Target.CPU);

            _isInitialized = true;
            _logger.LogInformation("人脸检测模型加载成功");
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化人脸检测模型时发生错误");
            return false;
        }
    }

    public async Task<FaceDetectionResult> DetectAsync(byte[] imageData, CancellationToken cancellationToken = default)
    {
        if (!_isInitialized || _net == null)
        {
            return new FaceDetectionResult
            {
                Status = DetectionStatus.NoFace,
                FaceCount = 0,
                ElapsedMilliseconds = 0,
                ConfidenceThreshold = _confidenceThreshold
            };
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            using var mat = Mat.FromImageData(imageData);
            if (mat.Empty())
            {
                _logger.LogWarning("无法解码图像数据");
                return CreateEmptyResult(stopwatch.ElapsedMilliseconds);
            }

            var faces = await DetectFacesInternalAsync(mat, cancellationToken);
            
            return new FaceDetectionResult
            {
                Status = faces.Count > 0 ? DetectionStatus.FaceDetected : DetectionStatus.NoFace,
                FaceCount = faces.Count,
                Faces = faces,
                ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                ConfidenceThreshold = _confidenceThreshold,
                ShouldCapture = faces.Count > 0
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "人脸检测时发生错误");
            return CreateEmptyResult(stopwatch.ElapsedMilliseconds);
        }
    }

    public async Task<IReadOnlyList<FaceDetectionResult>> DetectBatchAsync(IReadOnlyList<byte[]> imageDataList, CancellationToken cancellationToken = default)
    {
        var results = new List<FaceDetectionResult>();
        
        foreach (var imageData in imageDataList)
        {
            cancellationToken.ThrowIfCancellationRequested();
            var result = await DetectAsync(imageData, cancellationToken);
            results.Add(result);
        }
        
        return results;
    }

    private async Task<IReadOnlyList<FaceRegion>> DetectFacesInternalAsync(Mat image, CancellationToken cancellationToken)
    {
        if (_net == null) return Array.Empty<FaceRegion>();

        var faces = new List<FaceRegion>();
        
        try
        {
            // 预处理图像
            var inputSize = new Size(320, 320);
            using var blob = CvDnn.BlobFromImage(image, 1.0, inputSize, new Scalar(104, 117, 123), false, false);
            
            _net.SetInput(blob);
            
            // 前向推理
            using var output = _net.Forward();
            
            // 解析输出
            var outputMat = output.Reshape(1, output.Size(1));
            
            for (int i = 0; i < outputMat.Rows; i++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var confidence = outputMat.At<float>(i, 14);
                
                if (confidence > _confidenceThreshold)
                {
                    var x = outputMat.At<float>(i, 0) * image.Width;
                    var y = outputMat.At<float>(i, 1) * image.Height;
                    var width = outputMat.At<float>(i, 2) * image.Width - x;
                    var height = outputMat.At<float>(i, 3) * image.Height - y;
                    
                    // 提取关键点（可选）
                    var landmarks = new List<Point2D>();
                    for (int j = 4; j < 14; j += 2)
                    {
                        var landmarkX = outputMat.At<float>(i, j) * image.Width;
                        var landmarkY = outputMat.At<float>(i, j + 1) * image.Height;
                        landmarks.Add(new Point2D { X = landmarkX, Y = landmarkY });
                    }
                    
                    var face = new FaceRegion
                    {
                        X = Math.Max(0, x),
                        Y = Math.Max(0, y),
                        Width = Math.Min(width, image.Width - x),
                        Height = Math.Min(height, image.Height - y),
                        Confidence = confidence,
                        Landmarks = landmarks
                    };
                    
                    faces.Add(face);
                }
            }
            
            // 非极大值抑制
            if (faces.Count > 1)
            {
                faces = ApplyNonMaxSuppression(faces);
            }
            
            // 限制检测数量
            if (faces.Count > _topK)
            {
                faces = faces.OrderByDescending(f => f.Confidence).Take(_topK).ToList();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "人脸检测内部处理时发生错误");
        }
        
        return faces;
    }

    private List<FaceRegion> ApplyNonMaxSuppression(List<FaceRegion> faces)
    {
        var result = new List<FaceRegion>();
        var sortedFaces = faces.OrderByDescending(f => f.Confidence).ToList();
        
        while (sortedFaces.Count > 0)
        {
            var bestFace = sortedFaces[0];
            result.Add(bestFace);
            sortedFaces.RemoveAt(0);
            
            for (int i = sortedFaces.Count - 1; i >= 0; i--)
            {
                var iou = CalculateIoU(bestFace, sortedFaces[i]);
                if (iou > _nmsThreshold)
                {
                    sortedFaces.RemoveAt(i);
                }
            }
        }
        
        return result;
    }

    private float CalculateIoU(FaceRegion face1, FaceRegion face2)
    {
        var x1 = Math.Max(face1.X, face2.X);
        var y1 = Math.Max(face1.Y, face2.Y);
        var x2 = Math.Min(face1.X + face1.Width, face2.X + face2.Width);
        var y2 = Math.Min(face1.Y + face1.Height, face2.Y + face2.Height);
        
        if (x2 <= x1 || y2 <= y1) return 0;
        
        var intersection = (x2 - x1) * (y2 - y1);
        var union = face1.Area + face2.Area - intersection;
        
        return intersection / union;
    }

    private FaceDetectionResult CreateEmptyResult(double elapsedMs)
    {
        return new FaceDetectionResult
        {
            Status = DetectionStatus.NoFace,
            FaceCount = 0,
            ElapsedMilliseconds = elapsedMs,
            ConfidenceThreshold = _confidenceThreshold
        };
    }

    public void SetParameters(float confidenceThreshold, float nmsThreshold, int topK)
    {
        _confidenceThreshold = Math.Clamp(confidenceThreshold, 0.1f, 1.0f);
        _nmsThreshold = Math.Clamp(nmsThreshold, 0.1f, 1.0f);
        _topK = Math.Max(1, topK);
        
        _logger.LogInformation("人脸检测参数已更新: Confidence={Confidence}, NMS={Nms}, TopK={TopK}", 
            _confidenceThreshold, _nmsThreshold, _topK);
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        try
        {
            _net?.Dispose();
            _net = null;
            _isInitialized = false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放人脸检测资源时发生错误");
        }
        
        _disposed = true;
    }
}
