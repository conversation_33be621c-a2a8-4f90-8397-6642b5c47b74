namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 捕获的图像信息
/// </summary>
public record CapturedImage
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public required string FilePath { get; init; }
    
    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName => Path.GetFileName(FilePath);
    
    /// <summary>
    /// 拍摄时间
    /// </summary>
    public required DateTime CaptureTime { get; init; }
    
    /// <summary>
    /// 摄像头名称
    /// </summary>
    public required string CameraName { get; init; }
    
    /// <summary>
    /// 图像分辨率
    /// </summary>
    public required Resolution Resolution { get; init; }
    
    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; init; }
    
    /// <summary>
    /// 检测到的人脸数量
    /// </summary>
    public int FaceCount { get; init; }
    
    /// <summary>
    /// 缩略图路径（可选）
    /// </summary>
    public string? ThumbnailPath { get; init; }
    
    /// <summary>
    /// 是否存在文件
    /// </summary>
    public bool FileExists => File.Exists(FilePath);
    
    /// <summary>
    /// 格式化的文件大小
    /// </summary>
    public string FormattedFileSize
    {
        get
        {
            if (FileSize < 1024) return $"{FileSize} B";
            if (FileSize < 1024 * 1024) return $"{FileSize / 1024.0:F1} KB";
            return $"{FileSize / (1024.0 * 1024.0):F1} MB";
        }
    }
}
