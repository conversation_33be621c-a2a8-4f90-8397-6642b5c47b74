using FaceRecognitionMonitoring.Core.Enums;

namespace FaceRecognitionMonitoring.Core.Models;

/// <summary>
/// 人脸检测结果
/// </summary>
public record FaceDetectionResult
{
    /// <summary>
    /// 检测状态
    /// </summary>
    public required DetectionStatus Status { get; init; }
    
    /// <summary>
    /// 检测到的人脸数量
    /// </summary>
    public int FaceCount { get; init; }
    
    /// <summary>
    /// 人脸区域列表
    /// </summary>
    public IReadOnlyList<FaceRegion> Faces { get; init; } = Array.Empty<FaceRegion>();
    
    /// <summary>
    /// 检测时间戳
    /// </summary>
    public DateTime Timestamp { get; init; } = DateTime.Now;
    
    /// <summary>
    /// 检测耗时（毫秒）
    /// </summary>
    public double ElapsedMilliseconds { get; init; }
    
    /// <summary>
    /// 置信度阈值
    /// </summary>
    public float ConfidenceThreshold { get; init; }
    
    /// <summary>
    /// 是否触发拍照
    /// </summary>
    public bool ShouldCapture { get; init; }
    
    /// <summary>
    /// 冷却剩余时间（秒）
    /// </summary>
    public double CooldownRemainingSeconds { get; init; }
}
