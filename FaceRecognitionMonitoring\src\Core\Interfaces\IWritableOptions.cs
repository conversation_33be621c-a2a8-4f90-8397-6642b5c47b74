namespace FaceRecognitionMonitoring.Core.Interfaces;

/// <summary>
/// 可写配置选项接口
/// </summary>
/// <typeparam name="T">配置类型</typeparam>
public interface IWritableOptions<T> where T : class
{
    /// <summary>
    /// 当前配置值
    /// </summary>
    T Value { get; }
    
    /// <summary>
    /// 更新配置
    /// </summary>
    /// <param name="updateAction">更新操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateAsync(Action<T> updateAction, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task SaveAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 重新加载配置
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task ReloadAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 配置变化事件
    /// </summary>
    event EventHandler<T>? OnChange;
}
