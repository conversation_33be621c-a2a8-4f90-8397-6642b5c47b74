using FaceRecognitionMonitoring.Core.Models;

namespace FaceRecognitionMonitoring.Core.Interfaces;

/// <summary>
/// 摄像头提供者接口
/// </summary>
public interface ICameraProvider : IDisposable
{
    /// <summary>
    /// 获取可用摄像头列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>摄像头信息列表</returns>
    Task<IReadOnlyList<CameraInfo>> GetAvailableCamerasAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 连接到指定摄像头
    /// </summary>
    /// <param name="cameraId">摄像头ID</param>
    /// <param name="resolution">分辨率</param>
    /// <param name="frameRate">帧率</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否连接成功</returns>
    Task<bool> ConnectAsync(int cameraId, Resolution? resolution = null, double frameRate = 30, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 断开当前摄像头连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task DisconnectAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 开始视频流
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>视频帧流</returns>
    IAsyncEnumerable<byte[]> StartStreamAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 停止视频流
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StopStreamAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 捕获单帧图像
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>图像数据</returns>
    Task<byte[]?> CaptureFrameAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取当前摄像头信息
    /// </summary>
    CameraInfo? CurrentCamera { get; }
    
    /// <summary>
    /// 是否已连接
    /// </summary>
    bool IsConnected { get; }
    
    /// <summary>
    /// 是否正在流式传输
    /// </summary>
    bool IsStreaming { get; }
    
    /// <summary>
    /// 摄像头状态变化事件
    /// </summary>
    event EventHandler<CameraInfo>? CameraStatusChanged;
    
    /// <summary>
    /// 摄像头列表变化事件（热插拔）
    /// </summary>
    event EventHandler? CameraListChanged;
}
