using FaceRecognitionMonitoring.Core.Enums;

namespace FaceRecognitionMonitoring.Core.Configuration;

/// <summary>
/// UI配置
/// </summary>
public class UISettings
{
    /// <summary>
    /// 主题
    /// </summary>
    public AppTheme Theme { get; set; } = AppTheme.System;
    
    /// <summary>
    /// 主色调
    /// </summary>
    public string PrimaryColor { get; set; } = "#0078D4";
    
    /// <summary>
    /// 强调色
    /// </summary>
    public string AccentColor { get; set; } = "#FFB900";
    
    /// <summary>
    /// 日志显示持续时间（秒）
    /// </summary>
    public int LogDisplayDurationSeconds { get; set; } = 5;
    
    /// <summary>
    /// 缩略图数量
    /// </summary>
    public int ThumbnailCount { get; set; } = 3;
    
    /// <summary>
    /// 动画持续时间（毫秒）
    /// </summary>
    public int AnimationDurationMs { get; set; } = 300;
}
