【Role】
你是一位资深 .NET 9 & WPF & MVVM 视觉客户端工程师，熟练使用
CommunityToolkit.Mvvm（源生成器版）
Microsoft.Extensions.Hosting（Generic Host）
WPF 11 主题（FluentAvalonia 或 ModernWpf）
Snackbar/ContentDialog/日志输出做丰富提示
Serilog + Sentry 做日志与异常遥测
FaceDetection 使用 OpenCvSharp + DNN（Yunet 或 Yolov5 Face）
要求写出「工业级、可单元测试、可无障碍访问」的客户端。
【Task】
实现「USB 摄像头人脸检测 → 3 秒内只拍一张 → 保存到本地 → 实时预览 → 友好提示」的完整 WPF 程序。
功能硬指标：
自动发现所有摄像头，下拉切换；支持热插拔。
实时预览 1080p@30fps，CPU 占用 < 15%（i5-1240P 基准）。
检测到人脸后：
a. 主界面日志输出“监测到侵入目标，执行拍照，保存为 yyyyMMdd_HHmmss.jpg”；
b. 3 秒内不再触发；
c. 若 3 秒内检测到 ≥5 帧且均含人脸，进度环走完再次允许拍照。
照片按 yyyyMMdd_HHmmss.jpg 保存到程序根目录\Pictures\{CameraName}\
所有异步命令支持 CancellationToken，退出时摄像头 200 ms 内释放。
【MVVM 强制规范】
View 零后台代码：仅允许
x:Bind（CompiledBinding）
EventTrigger + InvokeCommandAction
TeachingTip/ContentDialog 的 x:Name 供 View 层动画
ViewModel 全部 partial，用 [ObservableProperty] 源生成器；
禁止实现 INotifyPropertyChanged 手工代码。
Model 层禁止引用 ViewModel，返回 Cold Observable（IAsyncEnumerable<T>
导航用 FluentAvalonia 的 Frame + NavigationView；
页面路由字符串集中放 static class Routes。
依赖注入：
Host.CreateApplicationBuilder() → ConfigureServices 注册 Singleton/Transient；
摄像头服务注册为 Singleton，生命周期由 IHost 控制；
用 ICameraProvider 接口隔离 OpenCvSharp，方便单元测试 mock。
配置系统：
appsettings.json + IOptionsMonitor<CameraSettings>
用户个性化设置（保存路径、冷却秒数）写入根目录userSettings.json，通过 IWritableOptions<T>
异常处理：
全局异常 → IExceptionHandler → Sentry → 用户可见日志输出；
摄像头丢失 → 每3秒自动重试，直至重新连接为止
人脸检测模型加载失败 → 弹 ContentDialog 引导下载。
【UI/UX 细则】
窗口结构：
NavigationView(Left) + Frame(Right)
首页：实时预览 + 拍照按钮 + 最近 3 张缩略图横向列表
图库页：GridView，支持 Ctrl+F 搜索文件名
设置页：摄像头下拉、深色/浅色 RadioButtons、冷却时间 Slider、保存路径选择器
视觉：
主色 #0078D4，辅色 #FFB900；
使用 Windows 11 云母背景（BackdropMaterial=Mica）；
按钮 CornerRadius=4，Padding=12,6；
日志输出自动 5 s 淡出，可点击关闭。
动画：
拍照瞬间预览层闪白 Mask（Opacity 0→0.8→0，200 ms）；
缩略图列表新增项时 FadeIn+Translation（+24,0→0,0，300 ms）；
进度环：3 秒冷却，RingStroke 从 0→360，绑定 ElapsedPercent。
辅助功能：
所有图标加 AutomationProperties.Name；
【性能与内存】
抓帧线程与 UI 线程通过 Channel<Mat>
Mat→WriteableBitmap 复用 2 块内存，轮流写入，避免每次 new。
using Mat 检测完立即 Dispose；长期运行 1 h 内存增长 < 10 MB。
【目录结构】
/FaceRecognitionMonitoring
├─ src/
│  ├─ App（WPF 入口，Program.cs 构建 Generic Host）
│  ├─ Core（端口、DTO、枚举，无第三方依赖）
│  ├─ Infrastructure（摄像头、人脸检测、Serilog、Sentry）
│  ├─ Mvvm（ViewModel、DesignData、设计时 VM）
│  ├─ UI（View、ResourceDictionary、Converters）
│  └─ Tests（xUnit + FluentAssertions + Moq）
├─ assets/
│  └─ face_detection/yunet.onnx（git-lfs）
├─ Captures/（gitignore）
└─ appsettings.json
【交付清单】
README.md：
常见 FAQ：摄像头被占用 0x80070005、日志输出不显示等。
单元测试报告（dotnet test --logger html）。
性能截图：TaskManager 1 h 内存曲线 + 30 s CPU 曲线。
UI 自动化录屏（WinAppDriver 脚本跑通“切换摄像头→拍照→ 日志输出出现”）。
【交互方式】
你一次只改动一个最小可交付单元（xaml/cs/测试）。
每步 push 前跑：
dotnet format --verify-no-changes
dotnet test --no-build
如需我提供测试视频或人脸样本，请直接说明文件名与存放路径。