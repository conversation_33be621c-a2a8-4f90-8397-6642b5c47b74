{"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "Sentry": {"Dsn": "", "Environment": "Development", "TracesSampleRate": 1.0, "Debug": true}, "Camera": {"DefaultResolution": {"Width": 1920, "Height": 1080}, "DefaultFrameRate": 30, "MaxCpuUsagePercent": 15, "AutoDiscoveryIntervalSeconds": 3, "ReconnectIntervalSeconds": 3}, "FaceDetection": {"ModelPath": "assets/face_detection/yunet.onnx", "ConfidenceThreshold": 0.6, "NmsThreshold": 0.3, "TopK": 5000, "CooldownSeconds": 3, "MinConsecutiveFrames": 5}, "Storage": {"DefaultSavePath": "Captures", "FileNameFormat": "yyyyMMdd_HHmmss", "SupportedFormats": ["jpg", "png"], "DefaultFormat": "jpg", "JpegQuality": 95}, "UI": {"Theme": "System", "PrimaryColor": "#0078D4", "AccentColor": "#FFB900", "LogDisplayDurationSeconds": 5, "ThumbnailCount": 3, "AnimationDurationMs": 300}}