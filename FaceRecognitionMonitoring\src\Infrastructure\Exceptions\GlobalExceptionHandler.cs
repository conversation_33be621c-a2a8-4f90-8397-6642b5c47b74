using Microsoft.Extensions.Logging;
using Sentry;
using System.Windows;

namespace FaceRecognitionMonitoring.Infrastructure.Exceptions;

/// <summary>
/// 全局异常处理器
/// </summary>
public class GlobalExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;
    private readonly IHub _sentryHub;

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, IHub sentryHub)
    {
        _logger = logger;
        _sentryHub = sentryHub;
    }

    /// <summary>
    /// 初始化全局异常处理
    /// </summary>
    public void Initialize()
    {
        // 处理未捕获的异常
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        
        // 处理任务调度器异常
        TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        
        // 处理WPF应用程序异常
        if (Application.Current != null)
        {
            Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;
        }
        
        _logger.LogInformation("全局异常处理器已初始化");
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        
        _logger.LogCritical(exception, "发生未处理的异常，应用程序即将终止");
        
        // 发送到Sentry
        _sentryHub.CaptureException(exception);
        
        // 显示用户友好的错误消息
        ShowErrorDialog("应用程序遇到严重错误", exception?.Message ?? "未知错误", true);
    }

    private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        _logger.LogError(e.Exception, "发生未观察到的任务异常");
        
        // 发送到Sentry
        _sentryHub.CaptureException(e.Exception);
        
        // 标记异常已处理，防止应用程序崩溃
        e.SetObserved();
        
        // 显示用户友好的错误消息
        ShowErrorDialog("后台任务错误", e.Exception.GetBaseException().Message, false);
    }

    private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        _logger.LogError(e.Exception, "发生UI线程未处理的异常");
        
        // 发送到Sentry
        _sentryHub.CaptureException(e.Exception);
        
        // 标记异常已处理，防止应用程序崩溃
        e.Handled = true;
        
        // 显示用户友好的错误消息
        ShowErrorDialog("界面操作错误", e.Exception.Message, false);
    }

    /// <summary>
    /// 处理已知异常
    /// </summary>
    /// <param name="exception">异常</param>
    /// <param name="context">上下文信息</param>
    public void HandleException(Exception exception, string? context = null)
    {
        var message = string.IsNullOrEmpty(context) 
            ? "发生异常" 
            : $"在 {context} 中发生异常";
            
        _logger.LogError(exception, message);
        
        // 发送到Sentry
        _sentryHub.CaptureException(exception, scope =>
        {
            if (!string.IsNullOrEmpty(context))
            {
                scope.SetTag("context", context);
            }
        });
        
        // 根据异常类型决定是否显示用户消息
        if (ShouldShowToUser(exception))
        {
            ShowErrorDialog("操作失败", GetUserFriendlyMessage(exception), false);
        }
    }

    private bool ShouldShowToUser(Exception exception)
    {
        // 某些异常类型不需要显示给用户
        return exception is not (
            OperationCanceledException or
            TaskCanceledException or
            ObjectDisposedException
        );
    }

    private string GetUserFriendlyMessage(Exception exception)
    {
        return exception switch
        {
            UnauthorizedAccessException => "没有足够的权限执行此操作",
            FileNotFoundException => "找不到所需的文件",
            DirectoryNotFoundException => "找不到指定的目录",
            IOException => "文件操作失败",
            OutOfMemoryException => "内存不足，请关闭其他应用程序后重试",
            TimeoutException => "操作超时，请检查网络连接",
            ArgumentException => "参数错误",
            InvalidOperationException => "当前状态下无法执行此操作",
            NotSupportedException => "不支持此操作",
            _ => exception.Message
        };
    }

    private void ShowErrorDialog(string title, string message, bool isTerminating)
    {
        try
        {
            Application.Current?.Dispatcher.Invoke(() =>
            {
                var fullMessage = isTerminating 
                    ? $"{message}\n\n应用程序将关闭。"
                    : message;
                    
                MessageBox.Show(
                    fullMessage,
                    title,
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "显示错误对话框时发生异常");
        }
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    public void Cleanup()
    {
        try
        {
            AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
            TaskScheduler.UnobservedTaskException -= OnUnobservedTaskException;
            
            if (Application.Current != null)
            {
                Application.Current.DispatcherUnhandledException -= OnDispatcherUnhandledException;
            }
            
            _logger.LogInformation("全局异常处理器已清理");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理全局异常处理器时发生错误");
        }
    }
}
