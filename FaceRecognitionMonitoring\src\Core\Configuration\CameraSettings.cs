using FaceRecognitionMonitoring.Core.Models;

namespace FaceRecognitionMonitoring.Core.Configuration;

/// <summary>
/// 摄像头配置
/// </summary>
public class CameraSettings
{
    /// <summary>
    /// 默认分辨率
    /// </summary>
    public Resolution DefaultResolution { get; set; } = Resolution.Presets.FullHD;
    
    /// <summary>
    /// 默认帧率
    /// </summary>
    public double DefaultFrameRate { get; set; } = 30;
    
    /// <summary>
    /// 最大CPU使用率百分比
    /// </summary>
    public int MaxCpuUsagePercent { get; set; } = 15;
    
    /// <summary>
    /// 自动发现间隔（秒）
    /// </summary>
    public int AutoDiscoveryIntervalSeconds { get; set; } = 3;
    
    /// <summary>
    /// 重连间隔（秒）
    /// </summary>
    public int ReconnectIntervalSeconds { get; set; } = 3;
}
